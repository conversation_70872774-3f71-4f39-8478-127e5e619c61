package com.imile.attendance.abnormal.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.DayAttendanceHandlerFormDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateCommonService;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 灵活打卡两次考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "FlexibleWorkTwiceCalculateServiceImpl")
public class FlexibleWorkTwiceCalculateServiceImpl extends AttendanceCalculateCommonService implements AttendanceCalculateService {
    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return CollectionUtils.isNotEmpty(userAttendancePunchConfigList)
                && Objects.equals(BusinessConstant.Y, userAttendancePunchConfigList.get(0).getIsActualPunch())
                && Objects.equals(PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode(), punchConfigType);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {
        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList = new ArrayList<>();

        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = calculateContext.getAttendanceEmployeeDetailDOList();
        List<EmployeeAbnormalAttendanceDO> userAbnormalAttendanceDOList = calculateContext.getUserAbnormalAttendanceDOList();

        //需要将当天已经存在的考勤全部删除，会重新计算(请假/外勤审批通过的肯定不能删除,不然会重复计算，浪费假期)
        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = attendanceEmployeeDetailDOList
                .stream().filter(item -> Objects.isNull(item.getFormId())).collect(Collectors.toList());
        updateEmployeeDetailDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });

        //没有被审批单关联的单据，可以删除的异常
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = userAbnormalAttendanceDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode())
                        || StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.REJECT.getCode())).collect(Collectors.toList());

        updateAbnormalAttendanceDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });

        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();

        List<UserAttendancePunchConfigDTO> userDayAttendancePunchConfigDTOList = calculateContext.getUserAttendancePunchConfigDTOList()
                .stream().filter(item -> item.getDayId().equals(calculateHandlerDTO.getAttendanceDayId())).collect(Collectors.toList());

        UserAttendancePunchConfigDTO userAttendancePunchConfig = userDayAttendancePunchConfigDTOList.get(0);
        Date earliestPunchInTime = userAttendancePunchConfig.getClassConfigDO().getClassItemConfigList().get(0).getEarliestPunchInTime();

        String earliestPunchInTimeString = DateHelper.formatHHMMSS(earliestPunchInTime);
        String earliestPunchInTimeDayString = DateHelper.formatYYYYMMDD(calculateHandlerDTO.getAttendanceTime());

        calculateHandlerDTO.setActualAttendanceStartTime(DateHelper.concatDateAndTime(earliestPunchInTimeDayString, earliestPunchInTimeString));
        calculateHandlerDTO.setActualAttendanceEndTime(DateHelper.pushDate(calculateHandlerDTO.getActualAttendanceStartTime(), 1));

        BigDecimal legalWorkingHours = userAttendancePunchConfig.getClassConfigDO().getLegalWorkingHours();
        BigDecimal attendanceHours = userAttendancePunchConfig.getClassConfigDO().getAttendanceHours();
        BigDecimal totalMinutes = legalWorkingHours.multiply(BusinessConstant.MINUTES);
        BigDecimal attendanceMinutes = attendanceHours.multiply(BusinessConstant.MINUTES);

        //把当天正常考勤中的已经关联过审批通过的单据的正常考勤排除掉,防止重复计算
        List<Long> usedFormIdList = attendanceEmployeeDetailDOList.stream().map(AttendanceEmployeeDetailDO::getFormId).filter(Objects::nonNull).collect(Collectors.toList());

        //需要先将改天的所有审批通过的请假/外勤拼接起来，一个时刻可以有多个审批通过的假期，每次请10分钟，请5次，就有5个单据
        List<DayAttendanceHandlerFormDTO> handlerFormDTOList = new ArrayList<>();
        dayFormInfoBuild(handlerFormDTOList, usedFormIdList, calculateContext.getUserPassFormBOList());
        handlerFormDTOList = handlerFormDTOList.stream().sorted(Comparator.comparing(DayAttendanceHandlerFormDTO::getStartTime)).collect(Collectors.toList());

        List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
        dayFormDateHandler(handlerFormDTOList, filterFormDTOList, calculateHandlerDTO);

        //查询当天生成的正常考勤的所有分钟(请假/外勤的 正常考勤会被删除，然后根据本次运行结果，看是否生成正常还是异常考勤)
        BigDecimal usedMinutes = dayExistMinutes(attendanceEmployeeDetailDOList);

        usedMinutes = punchDayLeaveHandler(usedMinutes, totalMinutes, filterFormDTOList, calculateHandlerDTO, calculateContext, addEmployeeDetailDOList);

        //请假时间都足够法定工作时长了
        if (usedMinutes.compareTo(totalMinutes) == 0) {
            attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);
            return;
        }

        List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList = calculateContext.getUserAttendancePunchConfigDTOList()
                .stream().filter(item -> item.getDayId().equals(calculateHandlerDTO.getAttendanceDayId())).collect(Collectors.toList());

        punchFreeDayInfoHandler(usedMinutes, totalMinutes, attendanceMinutes, calculateHandlerDTO, userAttendancePunchConfigList.get(0), calculateContext, addEmployeeDetailDOList, addAbnormalAttendanceDOList);

        //过滤审批中的异常
        List<String> inReviewAbnormalTypeList = userAbnormalAttendanceDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode()))
                .map(EmployeeAbnormalAttendanceDO::getAbnormalType).collect(Collectors.toList());
        addAbnormalAttendanceDOList = addAbnormalAttendanceDOList.stream().filter(abnormal -> !inReviewAbnormalTypeList.contains(abnormal.getAbnormalType())).collect(Collectors.toList());

        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);

    }

    private void punchFreeDayInfoHandler(BigDecimal usedMinutes,
                                         BigDecimal totalMinutes,
                                         BigDecimal attendanceMinutes,
                                         AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                         UserAttendancePunchConfigDTO userAttendancePunchConfigDTO,
                                         AttendanceCalculateContext calculateContext,
                                         List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                         List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList) {
        //休假时间不够，需要看打卡数据
        List<UserPunchRecordBO> itemPunchRecordList = calculateContext.getPunchRecordDOList().stream()
                .filter(item -> item.getFormatPunchTime().compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) > -1
                        && item.getFormatPunchTime().compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) < 1)
                .sorted(Comparator.comparing(UserPunchRecordBO::getFormatPunchTime)).collect(Collectors.toList());

        //需要打卡间距的时间
        BigDecimal needPresentMinutes = attendanceMinutes.subtract(usedMinutes);

        //么有打卡数据，直接上下班缺卡异常
        if (CollectionUtils.isEmpty(itemPunchRecordList)) {
            //2条异常，上下班都缺卡
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(calculateHandlerDTO.getActualAttendanceStartTime());
            EmployeeAbnormalAttendanceDO beforeLackAbnormalAttendanceDO = buildAbnormal(calculateContext.getUser(), calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(),
                    calculateContext.getAttendanceType(), calculateContext.getPunchConfigDO().getId(), userAttendancePunchConfigDTO.getClassConfigDO().getId(),
                    userAttendancePunchConfigDTO.getClassConfigDO().getClassItemConfigList().get(0).getId(), JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(beforeLackAbnormalAttendanceDO);

            abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(calculateHandlerDTO.getActualAttendanceStartTime(), needPresentMinutes.intValue()));
            EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(calculateContext.getUser(), calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(),
                    calculateContext.getAttendanceType(), calculateContext.getPunchConfigDO().getId(), userAttendancePunchConfigDTO.getClassConfigDO().getId(),
                    userAttendancePunchConfigDTO.getClassConfigDO().getClassItemConfigList().get(0).getId(), JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
            return;
        }

        //只有一个打卡
        if (itemPunchRecordList.size() == 1) {
            BigDecimal beforeMinutes = BigDecimal.valueOf(DateUtil.between(calculateHandlerDTO.getActualAttendanceStartTime(), itemPunchRecordList.get(0).getFormatPunchTime(), DateUnit.MINUTE));
            BigDecimal afterMinutes = BigDecimal.valueOf(DateUtil.between(calculateHandlerDTO.getActualAttendanceEndTime(), itemPunchRecordList.get(0).getFormatPunchTime(), DateUnit.MINUTE));
            if (beforeMinutes.compareTo(afterMinutes) < 1) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), needPresentMinutes.intValue()));
                EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(calculateContext.getUser(), calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(),
                        calculateContext.getAttendanceType(), calculateContext.getPunchConfigDO().getId(), userAttendancePunchConfigDTO.getClassConfigDO().getId(),
                        userAttendancePunchConfigDTO.getClassConfigDO().getClassItemConfigList().get(0).getId(), JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
                return;
            }
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), needPresentMinutes.multiply(BigDecimal.valueOf(-1)).intValue()));
            EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(calculateContext.getUser(), calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(),
                    calculateContext.getAttendanceType(), calculateContext.getPunchConfigDO().getId(), userAttendancePunchConfigDTO.getClassConfigDO().getId(),
                    userAttendancePunchConfigDTO.getClassConfigDO().getClassItemConfigList().get(0).getId(), JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
            return;
        }

        //有多个打卡记录
        BigDecimal presentMinutes = BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE));
        if (presentMinutes.add(usedMinutes).compareTo(attendanceMinutes) > -1) {
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(calculateContext.getUser(), calculateHandlerDTO, calculateContext.getAttendanceType(),
                    AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO,
                    totalMinutes.subtract(usedMinutes), BigDecimal.ZERO, null);
            addEmployeeDetailDOList.add(userAttendance);
            return;
        }

        //特殊逻辑，看第一次打卡是在改天中间打卡时间的前面还是后面
        Date middleDate = DateUtil.offsetHour(calculateHandlerDTO.getActualAttendanceStartTime(), 12);
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(middleDate) < 1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), needPresentMinutes.intValue()));
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(calculateContext.getUser(), calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(),
                    calculateContext.getAttendanceType(), calculateContext.getPunchConfigDO().getId(), userAttendancePunchConfigDTO.getClassConfigDO().getId(),
                    userAttendancePunchConfigDTO.getClassConfigDO().getClassItemConfigList().get(0).getId(), JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
        abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
        abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), needPresentMinutes.multiply(BigDecimal.valueOf(-1)).intValue()));
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(calculateContext.getUser(), calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(),
                calculateContext.getAttendanceType(), calculateContext.getPunchConfigDO().getId(), userAttendancePunchConfigDTO.getClassConfigDO().getId(),
                userAttendancePunchConfigDTO.getClassConfigDO().getClassItemConfigList().get(0).getId(), JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
    }

    private BigDecimal punchDayLeaveHandler(BigDecimal usedMinutes,
                                            BigDecimal totalMinutes,
                                            List<DayAttendanceHandlerFormDTO> filterFormDTOList,
                                            AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                            AttendanceCalculateContext calculateContext,
                                            List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        //先构建请假
        for (DayAttendanceHandlerFormDTO handlerFormDTO : filterFormDTOList) {
            BigDecimal leaveMinutes = BigDecimal.valueOf(DateUtil.between(handlerFormDTO.getStartTime(), handlerFormDTO.getEndTime(), DateUnit.MINUTE));
            if (leaveMinutes.add(usedMinutes).compareTo(totalMinutes) > -1) {
                leaveMinutes = totalMinutes.subtract(usedMinutes);
            }
            if (leaveMinutes.compareTo(BigDecimal.ZERO) < 1) {
                return usedMinutes;
            }
            //看是不是外勤
            if (StringUtils.isBlank(handlerFormDTO.getLeaveType())) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(calculateContext.getUser(), calculateHandlerDTO, calculateContext.getAttendanceType(),
                        AttendanceConcreteTypeEnum.OOO.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO, leaveMinutes,
                        BigDecimal.ZERO, handlerFormDTO.getFormId());
                addEmployeeDetailDOList.add(userAttendance);
                usedMinutes = usedMinutes.add(leaveMinutes);
                continue;
            }
            //请假
            List<CompanyLeaveConfigDO> companyLeaveList;
            List<UserLeaveDetailDO> leaveDetailList;
            if (Objects.nonNull(handlerFormDTO.getConfigId())) {
                companyLeaveList = calculateContext.getUserCompanyLeaveConfigDOList().stream()
                        .filter(item -> item.getId().equals(handlerFormDTO.getConfigId()))
                        .collect(Collectors.toList());
                leaveDetailList = calculateContext.getUserLeaveDetailDOList().stream()
                        .filter(item -> item.getConfigId().equals(handlerFormDTO.getConfigId()))
                        .collect(Collectors.toList());
            } else {
                companyLeaveList = calculateContext.getUserCompanyLeaveConfigDOList().stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveName(), handlerFormDTO.getLeaveType()))
                        .collect(Collectors.toList());
                leaveDetailList = calculateContext.getUserLeaveDetailDOList().stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveName(), handlerFormDTO.getLeaveType()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(companyLeaveList)) {
                return usedMinutes;
            }
            if (CollectionUtils.isEmpty(leaveDetailList)) {
                return usedMinutes;
            }
            List<Long> leaveIdList = leaveDetailList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
            //根据百分比降序，优先使用百分比最高的
            List<UserLeaveStageDetailDO> leaveStageDetailDOList = calculateContext.getUserLeaveStageDetailDOList().stream()
                    .filter(item -> leaveIdList.contains(item.getLeaveId()))
                    .sorted(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaveStageDetailDOList)) {
                return usedMinutes;
            }
            leaveInfoBuild(calculateContext.getUser(), calculateHandlerDTO, handlerFormDTO.getFormId(), companyLeaveList.get(0), leaveStageDetailDOList,
                    leaveMinutes, calculateContext.getAttendanceType(), addEmployeeDetailDOList);
            usedMinutes = usedMinutes.add(leaveMinutes);
        }
        return usedMinutes;
    }


    private void dayFormInfoBuild(List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                  List<Long> usedFormIdList,
                                  List<AttendanceFormDetailBO> userPassFormBOList) {
        for (AttendanceFormDetailBO attendanceFormDetailBO : userPassFormBOList) {
            AttendanceFormDO formDO = attendanceFormDetailBO.getFormDO();
            if (usedFormIdList.contains(formDO.getId())) {
                continue;
            }
            List<AttendanceFormAttrDO> userPassFormAttrDOList = attendanceFormDetailBO.getAttrDOList();
            //请假
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                List<AttendanceFormAttrDO> leaveStartDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> leaveEndDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> configIdDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.configID.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> leaveNameDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(leaveStartDateDO) || CollectionUtils.isEmpty(leaveEndDateDO) || CollectionUtils.isEmpty(leaveNameDO)) {
                    continue;
                }

                Date leaveStartDate = DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateDO.get(0).getAttrValue());
                Date leaveEndDate = DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateDO.get(0).getAttrValue());

                Long leaveStartDayId = DateHelper.getDayId(leaveStartDate);
                Long leaveEndDayId = DateHelper.getDayId(leaveEndDate);

                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                if (CollectionUtils.isNotEmpty(configIdDO) && Objects.nonNull(configIdDO.get(0).getAttrValue())) {
                    dayAttendanceHandlerFormDTO.setConfigId(Long.valueOf(configIdDO.get(0).getAttrValue()));
                }
                dayAttendanceHandlerFormDTO.setLeaveType(leaveNameDO.get(0).getAttrValue());
                dayAttendanceHandlerFormDTO.setStartTime(leaveStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(leaveStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(leaveEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(leaveEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }

            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                List<AttendanceFormAttrDO> outOfOfficeStartDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> outOfOfficeEndDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(outOfOfficeStartDateDO) || CollectionUtils.isEmpty(outOfOfficeEndDateDO)) {
                    continue;
                }

                Date outOfOfficeStartDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateDO.get(0).getAttrValue());
                Date outOfOfficeEndDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateDO.get(0).getAttrValue());

                Long outOfOfficeStartDayId = DateHelper.getDayId(outOfOfficeStartDate);
                Long outOfOfficeEndDayId = DateHelper.getDayId(outOfOfficeEndDate);

                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                dayAttendanceHandlerFormDTO.setStartTime(outOfOfficeStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(outOfOfficeStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(outOfOfficeEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(outOfOfficeEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }
        }
    }

    private void dayFormDateHandler(List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                    List<DayAttendanceHandlerFormDTO> filterFormDTOList,
                                    AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        for (DayAttendanceHandlerFormDTO handlerFormDTO : handlerFormDTOList) {
            if (handlerFormDTO.getEndTime().compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1) {
                continue;
            }
            if (handlerFormDTO.getStartTime().compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
                continue;
            }
            if (handlerFormDTO.getStartTime().compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                    && handlerFormDTO.getEndTime().compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
                handlerFormDTO.setStartTime(calculateHandlerDTO.getActualAttendanceStartTime());
                handlerFormDTO.setEndTime(calculateHandlerDTO.getActualAttendanceEndTime());
                filterFormDTOList.add(handlerFormDTO);
                continue;
            }
            if (handlerFormDTO.getStartTime().compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) > -1
                    && handlerFormDTO.getEndTime().compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) < 1) {
                filterFormDTOList.add(handlerFormDTO);
                continue;
            }
            //交集
            if (handlerFormDTO.getStartTime().compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1) {
                handlerFormDTO.setStartTime(calculateHandlerDTO.getActualAttendanceStartTime());
                filterFormDTOList.add(handlerFormDTO);
                continue;
            }
            handlerFormDTO.setEndTime(calculateHandlerDTO.getActualAttendanceEndTime());
            filterFormDTOList.add(handlerFormDTO);
        }
    }
}
